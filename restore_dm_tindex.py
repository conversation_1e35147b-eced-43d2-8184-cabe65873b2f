#!/usr/bin/env python3
"""
Скрипт для восстановления dm_tindex в таблице department из CSV файла
"""

import csv
import psycopg2
from psycopg2.extras import execute_values
import os

# Настройки подключения к БД (замените на ваши)
DB_CONFIG = {
    'host': '*************',  # или IP сервера
    'database': 'geonpi',
    'user': 'kazpostgeo',
    'password': 'NLhtRQhR99y5BERs0HuP',
    'port': 5000
}

def restore_dm_tindex():
    """БЫСТРОЕ восстановление dm_tindex из CSV файла"""

    csv_file_path = 'kosyak/department.csv'

    if not os.path.exists(csv_file_path):
        print(f"Файл {csv_file_path} не найден!")
        return

    try:
        # Подключение к БД
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()

        print("Подключение к БД установлено")

        # БЫСТРОЕ чтение и обработка батчами
        batch_size = 1000
        updates_data = []
        total_rows = 0
        valid_rows = 0

        print("Быстрое чтение CSV...")

        with open(csv_file_path, 'r', encoding='utf-8') as file:
            reader = csv.reader(file)

            for row in reader:
                total_rows += 1

                if len(row) >= 13:  # Минимум столбцов
                    try:
                        dept_id = int(row[0])  # id
                        dm_tindex = row[12].strip() if len(row) > 12 else ''  # dm_tindex

                        # Только непустые dm_tindex
                        if dm_tindex:
                            updates_data.append((dm_tindex, dept_id))
                            valid_rows += 1

                            # Обновляем батчами для скорости
                            if len(updates_data) >= batch_size:
                                print(f"Обновляем батч {valid_rows//batch_size}...")
                                cur.executemany(
                                    "UPDATE public.department SET dm_tindex = %s WHERE id = %s",
                                    updates_data
                                )
                                conn.commit()
                                updates_data = []

                    except (ValueError, IndexError):
                        continue

        # Обновляем оставшиеся записи
        if updates_data:
            print("Обновляем последний батч...")
            cur.executemany(
                "UPDATE public.department SET dm_tindex = %s WHERE id = %s",
                updates_data
            )
            conn.commit()

        print(f"✅ ГОТОВО! Обработано {total_rows} строк, обновлено {valid_rows} записей")

        # Быстрая проверка
        cur.execute("SELECT COUNT(*) FROM public.department WHERE dm_tindex != '------'")
        result = cur.fetchone()[0]
        print(f"📊 Записей с dm_tindex в БД: {result}")

    except Exception as e:
        print(f"❌ Ошибка: {e}")
        if 'conn' in locals():
            conn.rollback()

    finally:
        if 'cur' in locals():
            cur.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    print("Восстановление dm_tindex из CSV файла")
    print("=" * 50)
    
    # Проверяем наличие psycopg2
    try:
        import psycopg2
    except ImportError:
        print("Ошибка: Не установлен psycopg2")
        print("Установите: pip install psycopg2-binary")
        exit(1)
    
    restore_dm_tindex()
