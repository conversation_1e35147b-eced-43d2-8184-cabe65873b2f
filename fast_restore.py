#!/usr/bin/env python3
"""
СУПЕР БЫСТРЫЙ скрипт для восстановления dm_tindex
"""

import csv
import psycopg2

# НАСТРОЙКИ БД - ЗАМЕНИТЕ НА СВОИ!
DB_CONFIG = {
    'host': '*************',  # или IP сервера
    'database': 'geonpi',
    'user': 'kazpostgeo',
    'password': 'NLhtRQhR99y5BERs0HuP',
    'port': 5000
}
def fast_restore():
    print("🚀 БЫСТРОЕ восстановление dm_tindex...")
    
    # Подключение
    conn = psycopg2.connect(**DB_CONFIG)
    cur = conn.cursor()
    
    # Читаем и обновляем сразу
    count = 0
    with open('kosyak/department.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        
        for row in reader:
            if len(row) >= 13 and row[12].strip():  # Есть dm_tindex
                try:
                    cur.execute(
                        "UPDATE geonpi.public.department SET dm_tindex = %s WHERE id = %s",
                        (row[12].strip(), int(row[0]))
                    )
                    count += 1
                    
                    if count % 500 == 0:
                        conn.commit()
                        print(f"⚡ Обновлено: {count}")
                        
                except:
                    continue
    
    conn.commit()
    cur.close()
    conn.close()
    
    print(f"✅ ГОТОВО! Обновлено {count} записей")

if __name__ == "__main__":
    fast_restore()
